import time
import types
import numpy as np
import json
import os
import elasticsearch
import elasticsearch.helpers
import elasticsearch.exceptions
from datetime import datetime
from collections import deque
from pprint import pprint as pp
from gaia.gaia_ml.family.fam_hf.sbert import sbert_base


def qi_ids( ids ):
    return {
        "ids": { "values": ids}
    }

def qi_mtl( mtl_ids, field_names, index ):
    '''
    More like this
    :param mtl_ids:
    :return:
    '''
    like_array = [{"_index": index, "_id": id} for id in mtl_ids]
    inner_query_mlt = {
        "more_like_this": {
            "fields": field_names,
            "like": mtl_ids,
            "min_doc_freq": 3,
            # "min_term_freq": 20,
            # "max_query_terms": 25,
            "include": True,
            "boost_terms": 1.0,
        }
    }

def qi_sort_script_sum( sum_var_list ):
    ss={
        "sort": {
            "_script": {
                "type": "number",
                "script": "return " + " + ".join([ " doc['"+sv+"'].value " for sv in sum_var_list ] ),
                #doc['view_count'].value + doc['comment_count'].value + doc['like_count'].value",
                "lang": "groovy",
                "order": "desc"
            }
        }
    }
    return ss

    ss={ "sort": {
        "_script": {
          "type": "number",
          "script": {
            "lang": "painless",
            "source": "doc['theatre'].value.length() * params.factor",
            "params": {
              "factor": 1.1
            }
          },
          "order": "asc"
        }
      }
    }
    return ss

def qi_querystring( query_string, field_names ):
    '''
    Query string
    '''
    qs = {
        "query_string": {
            "fields": field_names,
            "query": query_string
        }
    }
    return qs



def qi_bool( query_list, operator="must" ):
    '''
    Query string
    '''
    qs = {
        "query": {
            operator: {
                "must": [  query_list ],
            }
        }
    }
    return qs





class GaiaElastico:

    """
    General Purpose Class for handling ElasticSearch connections,
    advanced queries, cluster stats
    and wraps Python ElasticSearch lib.
    """

    def __init__(self, logfile_path="./gaia_elastico.log"):
        self.perpage = 100
        self.es = None
        self.alias_conn = None
        self.logfile_path = logfile_path
        if 0:
            #  new
            self.knn_type = 'EMB_02_xlm'
            self.knn_name = 'knn_' + self.knn_type
            self.knn_size = sbert_base.model_dict[self.knn_type]['vector_size']
        if 1:
            #  legacy
            self.knn_name = 'knn'
            self.knn_size = 1024

        #  get alias mapping from file
        base_dir = os.path.dirname(__file__)
        with open(base_dir + '/elasticsearch_conn_map.json', 'r') as f:
            self.es_conn_map = json.load(f)

    def connect(self, alias=None, port=None, host=None, timeout=60):
        print('elasticsearch connection alias', alias)
        if alias:
            try:
                self.alias_conn = self.es_conn_map[alias]
                self.es = elasticsearch.Elasticsearch(
                    self.alias_conn['endpoint'],
                    port=self.alias_conn['port'],
                    timeout=timeout,
                    max_retries=3,
                    retry_on_timeout=True
                )
                print('elasticsearch connection details: ', self.details)
            except KeyError as e:
                raise KeyError('elastic search map not found for alias {}'.format(alias))
        else:
            self.es = elasticsearch.Elasticsearch(
                host=host, port=port, timeout=timeout, max_retries=3, retry_on_timeout=True)

    @property
    def details(self):
        return self.alias_conn

    def list_indexes(self):
        indexes = self.es.indices.get_alias("*")
        return indexes

    def index_create(self, index):
        body = {
            "settings": {
                "index.mapping.total_fields.limit": 2000,
                "index.refresh_interval": "120s",
                #  https://stackoverflow.com/questions/41677198/result-window-is-too-large-from-size-must-be-less-than-or-equal-to-10000-b
                "max_result_window": 500000,
            },
        }
        self.es.indices.create(index=index, body=body)

    def index_create_knn(self, index, knn_space='cosinesimil'):
        print('create knn mapping with dimensions and name: ', self.knn_size)
        print('knn_name: ', self.knn_name)
        body = {
            "settings": {
                "index.mapping.total_fields.limit": 10000,
                "index.refresh_interval": "30s",
                "index.knn": True,
                "index.knn.space_type": knn_space,
                "index.number_of_replicas": 0,
                "index.number_of_shards": 3,
            },
            "mappings": {
                "properties": {
                    self.knn_name: {
                        "type": "knn_vector",
                        "dimension": self.knn_size
                    }
                },
                "gaia_added_ts": {
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis"
                }
            }
        }

        pre_loading_string = {
            "type": "text",
            "fielddata": True
        }

        pre_loading_int = {
            "type": "integer",
        }

        pre_loading_long = {
            "type": "long",
        }

        for field in ['company_name', 'description', 'short_description', 'cb_url', 'alias1']:
            body['mappings']['properties'][field] = pre_loading_string

        for field in ['dealer.edu_rank_empirical1', 'dealer.edu_rank_geo_country_empirical1',
                      'dealer.edu_rank_geo_region_empirical1', 'dealer.edu_rank_geo_subregion_empirical1',
                      'dealer.edu_rank_nature1','dealer.edu_rank_times1',
                      'dealer.max_inv_acq_count', 'dealer.max_inv_comp_count', 'dealer.max_inv_ipo_count',
                      'dealer.degree_grad_any', 'dealer.degree_grad_count', 'dealer.degree_master_any', 'dealer.degree_master_count',
                      'dealer.degree_phd_any', 'dealer.degree_phd_count', 'dealer.ppl_count',
                      'investors.inv_acq_count', 'investors.inv_comp_count', 'investors.inv_ipo_count']:
            body['mappings']['properties'][field] = pre_loading_int

        for field in ['funding_total_usd']:
            body['mappings']['properties'][field] = pre_loading_long

        self.es.indices.create(index=index, body=body)

    def insert_bulk(self, bulks=None, multi=False, status_log_path='/var/log/builder/agsearch_status.log'):
        """
        Bulk Insert. Can be single threaded or parallel (multi).
        """
        print('GAIAELASTICO: Insert bulks... ')

        res = None
        #logfile = open(self.logfile_path, "a+")

        if isinstance(bulks, types.GeneratorType):
            bulks = list(bulks)
        tries = 1

        while True:
            print('GAIAELASTICO: insert attempt number {}'.format(tries))
            #for b in bulks: b['_source']['knn'] = b['_source']['knn'][0]
            try:
                if multi:
                    print('GAIAELASTICO: insert parallel_bulk. size: {}'.format(len(bulks)))
                    deque(elasticsearch.helpers.parallel_bulk(self.es, bulks, thread_count=8, chunk_size=50, max_chunk_bytes=209600001))
                else:
                    print('GAIAELASTICO: insert single threaded bulk. size: {}.'.format(len(bulks)))
                    res = elasticsearch.helpers.bulk(self.es, bulks, raise_on_error=True)
                #  successful insert
                print('GAIAELASTICO: insert complete.')
                #logfile.close()
                if '_id' in bulks[0].keys():
                    print('worker: save status...', bulks[0]['_id'])
                    with open(status_log_path, 'w') as f:
                        f.write(bulks[0]['_id'])
                return res

            except elasticsearch.helpers.errors.BulkIndexError as e:
                print('Bulk index too many fields error...',str(e)[:1000])
                time.sleep(60)
                for bulk in bulks:
                    try:
                         res = elasticsearch.helpers.bulk(self.es, [bulk,], raise_on_error=True)
                    except Exception as e:
                        print('...BulkIndexError, individual insert', [bulk['_id']])
                if tries >= 3:
                    print('BulkIndexError, too many retries')
                    #logfile.close()
                    return False
                time.sleep(30*tries)
                tries += 1


            except UnicodeError as e:
                print('unicode error found, retrying insert for each record...', e)
                for bulk in bulks:
                    try:
                        res = elasticsearch.helpers.bulk(self.es, [bulk,], raise_on_error=True)
                    except Exception as e:
                        print('unicode error, deleting babyspider data and inserting...', bulk['_id'])
                        #import pdb; pdb.set_trace()
                        bulk['_source']['babyspider'] = {'error': 'unicode error'}
                        res = elasticsearch.helpers.bulk(self.es, [bulk,], raise_on_error=True)

            except Exception as e:
                #  generic Exception. Backoff and retry.
                print('GAIAELASTICO Generic Error: retrying...', str(e))
                #logfile.write('Error: {}'.format(e))
                #logfile.write('Companies: {}'.format([r['_id'] for r in bulks]))
                if 'Data too large' in str(e):
                    for bulk in bulks:
                        try:
                            print('inner retry of Data too large...')
                            res = elasticsearch.helpers.bulk(self.es, [bulk,], raise_on_error=True)
                        except Exception as e:
                            print('inner retry error, removing babsypider data', e)
                            bulk['_source']['babyspider'] = {'error': 'unicode error'}
                            res = elasticsearch.helpers.bulk(self.es, [bulk,], raise_on_error=True)
                if tries >= 3:
                    #logfile.close()
                    raise e
                print(f'worker sleeping for {120*tries}')
                time.sleep(120*tries)
                tries += 1

    def get_record_by_id(self, index=None, uuid=None):
        query_body = {
            "query": {
                "match": {
                    "_id": uuid
                }
            }
        }
        res = self.es.search(index=index, body=query_body)
        return res['hits']['hits']

    def latest_record(self, index=None):
        """
        Gets latest record by created_at date for single index.
        """
        latest = None
        kwargs = {
            'field_names': [],
            'query_string': '*',
            'sorting': {
                u'value': u'desc',
                u'key': u'created_at',
                u'mode': u'max'
            },
            'pp_count': 1
        }
        res, query_json = self.docs_query(index, **kwargs)
        data = res['hits']['hits'][0]['_source']
        latest = {
            'uuid': data['uuid'],
            'date': datetime.strptime(data['created_at'], '%Y-%m-%dT%H:%M:%S')
        }
        return latest

    def after_date(self, index=None, date=None, pp_from=0, pp_count=1000, scroll_id=None):
        query_body = {
            "query": {
                "range": {
                    "created_at": {
                        "gte": date,
                    }
                }
            },
            "sort": [
                {"created_at": {"order": "desc"}}
            ]
        }
        res = self.es.search(
            from_=pp_from, size=pp_count, index=index, body=query_body)
        return res['hits']['hits']

    def record_count(self, index):
        """
        Returns an object with record counts, index name as key.
        Accepts string or list of strings.
        """
        if isinstance(index, list):
            results = []
            for idx in index:
                results.append({idx: self.es.cat.count(idx, params={"format": "json"})[0]})
            return results
        return {index: self.es.cat.count(index, params={"format": "json"})[0]}

    @staticmethod
    def _sorting(sorting=None):
        """
        Construct Sorting JSON structure.
        """
        sort = []
        if sorting:
            sort_body = {
                sorting['key']: {
                    'order': sorting['value'], 'mode': sorting['mode']
                }
            }
            sort.append(sort_body)
        sort.append('_score')
        return sort

    @staticmethod
    def _filtering(filter_kwargs=None):
        """
        Construct Filtering JSON structure.
        """
        operator_map = {'>': 'gte', '<': 'lte'}
        filters = []
        if filter_kwargs:
            for key, val in filter_kwargs.items():
                #  example kwargs
                #  {'dealer.incl': '>0.5'}
                op = val[0]
                operator = operator_map.get(op)
                if operator:
                    range = {operator: val[1:]}
                    filters.append({'range': {key: range}})
        #   add filters to bool query
        return filters

    def index_delete(self, index):
        self.es.indices.delete(index=index, ignore=[400, 404])

    def doc_insert(self, dictrow, id, idx):
        print('inserting ', idx, id)
        doc_type = '_doc'  # idx
        try:
            self.es.index(index=idx, doc_type=doc_type, id=id, body=dictrow)
        except elasticsearch.exceptions.RequestError as e:
            print('FAILED_IMPORT')
            print(e.info)

    def doc_get(self, index, id, ):
        doc_type = index
        return self.es.get(index=index, doc_type=doc_type, id=id)

    def docs_mget(self, index, ids, sorting={}, filter_kwargs=None, pp_from=0, pp_count=None, max_size=2000):
        '''
        Get Documents by ID.
        '''
        inner_query = {
            "ids": {
                "values": ids
            }
        }
        query_body = {
            "from": 0,
            "size": max_size,
            "query": {
                "bool": {
                    "must": [
                        inner_query
                    ]
                }
            }
        }
        #  filters
        if filter_kwargs:
            query_body[
                "query"][
                "bool"][
                "filter"] = self._filtering(filter_kwargs)
        #  sorting
        if sorting:
            query_body["sort"] = self._sorting(sorting)

        #  query
        res = self.es.search(
            from_=pp_from, size=pp_count, index=index, body=query_body)
        #  result
        return res, query_body

    def docs_knn(self, index, vector, fld, k=10, pp_from=0, pp_count=None):
        query = {
            "size": k,
            "query": {
                "knn": {
                     fld: {
                         "vector": vector,
                          "k": k
                     }
                 }
             }
        }
        # pp(query)

        query = {
            "size": 100,
            "query": {
                "knn": {
                    "knn": {
                        "vector": vector,
                        "k": 100
                     },
                 },
            }
        }

        res = self.es.search(from_=pp_from, size=pp_count, index=index, body=query)
        return res, query

    def average_vector(self, index, doc_ids):
        docs = self.docs_mget(index, doc_ids)
        res = docs[0]['hits']['hits']
        vals_sum = None
        n = 0
        for doc in res:
            val = doc['_source'][self.knn_name]
            val_np = np.array(val)
            #  no vector
            if None in val_np:
                continue
            if vals_sum is None:
                vals_sum = val_np
            else:
                vals_sum = vals_sum + val_np
            n += 1
        return vals_sum / n

    def _find_duplicates(self, index=None, key=None):
        #  key = _id
        #  key = basics.org_id
        query_body = {
            "size": 0,
            "aggs": {
                "duplicateNames": {
                    "terms": {
                        "field": key,
                        "min_doc_count": 2
                    }
                }
            }
        }
        #  query
        res = self.es.search(index=index, body=query_body)
        #  result
        return res, query_body

    def docs_search_all(self, index, pp_from=0, pp_count=None):
        query = {"query": {"match_all": {}}}
        res = self.es.search(from_=pp_from, size=pp_count, index=index, body=query)
        return res, query

    # iterator
    def docs_scan(self, index, pp_count=None, clear_scroll=True):
        query = {"query": {"match_all": {}}}
        res = elasticsearch.helpers.scan(client=self.es, query=query, scroll='5m', size=pp_count, index=index,
                                         clear_scroll=clear_scroll, )
        return res, query

    def docs_query_morelike(self, index, field_names, ids, query_string=None, pp_from=0, pp_count=None, filter_kwargs=None, sorting=None):
        ''''
        like_array = [
            {
                "_index": "idx_comp10",
                "_id": "4fa8859f-a6c1-92c3-52eb-3722c681d152"
            },
            {
                "_index": "idx_comp10",
                "_id": "14b2722c-988b-fb1f-969a-6b658e3cbb82"
            },
            {
                "_index": "idx_comp10",
                "_id": "f04fcb90-5433-4c30-8184-f1713f8ca9a4"
            },
        ]
        '''

        like_array = [{"_index": index, "_id": id} for id in ids]

        inner_query_mlt = {
            "more_like_this": {
                "fields": field_names,
                "like": like_array,
                "min_doc_freq": 3,
                #"min_term_freq": 20,
                #"max_query_terms": 25,
                "include": True,
                "boost_terms": 1.0,
            }
        }

        inner_query_list = [ inner_query_mlt, ]

        if query_string:
            qs = {
                "query_string": {
                        "fields": field_names,
                        "query": query_string
                }
            }
            inner_query_list.append( qs )

        query_body = {
            "query": {
                "bool": {
                    "must": inner_query_list
                }
            }
        }
        pp( query_body)

        #  filters
        if filter_kwargs:
            query_body[
                "query"][
                "bool"][
                "filter"] = self._filtering(filter_kwargs)
        #  sorting
        if sorting:
            query_body["sort"] = self._sorting(sorting)

        if not pp_count:
            pp_count = self.perpage
        res = self.es.search(index=index, body=query_body, from_=pp_from, size=pp_count)
        return res, query_body


    def docs_query_name_search(self, index, field_names, query_string, filter_kwargs={}, sorting={}, pp_from=0, pp_count=None):

        #  default is an empty string
        sub_queries = ''

        #  filters: construct additional query strings
        if filter_kwargs:
            for key, val in filter_kwargs.items():
                sub_queries += ' AND ({}:{})'.format(key, val)

        query_inner = {
            "query_string": {
                "fields": field_names,
                "analyze_wildcard": "true",
                #"minimum_should_match": "100%",
                "query": query_string + sub_queries
            }
        }

        #  company name search
        shoulds = [{ "match_phrase": { "company_name": org.strip() } } for org in query_string.split(',')]
        query_inner = {"bool": {
            "should": shoulds,
            "minimum_should_match": 1
            }
        }
        query_body = {
            "query": query_inner,
        }

        sorting={'key': 'company_name', 'value': 'asc', 'mode': 'max'}
        #raise ValueError(f'{sorting}')
        #  sorting
        if sorting:
            query_body["sort"] = self._sorting(sorting)

        if not pp_count:
            pp_count = self.perpage

        res = self.es.search(index=index, body=query_body, from_=pp_from, size=pp_count)
        return res, query_body



    def docs_query(self, index, field_names, query_string, filter_kwargs={}, sorting={}, pp_from=0, pp_count=None):
        """
        Logical Query.
        """

        #  default is an empty string
        sub_queries = ''

        #  filters: construct additional query strings
        if filter_kwargs:
            for key, val in filter_kwargs.items():
                sub_queries += ' AND ({}:{})'.format(key, val)

        query_inner = {
            "query_string": {
                "fields": field_names,
                "analyze_wildcard": "true",
                #"minimum_should_match": "100%",
                "query": query_string + sub_queries
            }
        }

        #  debug
        #raise ValueError(query_string)
        query_inner_UNUSED = {
            "query_string": {
                "fields": field_names,
                #"analyze_wildcard": "true",
                #"minimum_should_match": "100%",
                #"query": "wholefoods* gomeral*"
                "analyze_wildcard": "true",
                "minimum_should_match": "100%",
                "query": query_string
            }
        }

        query_body = {
            "query": query_inner,
        }
        #  sorting
        if sorting:
            query_body["sort"] = self._sorting(sorting)

        if not pp_count:
            pp_count = self.perpage

        res = self.es.search(index=index, body=query_body, from_=pp_from, size=pp_count)
        print('QUERY BODY: ', query_body)
        return res, query_body

    # FFS!
    # Boosting Query with negative_boost does not work as expected on 6.x
    # https://github.com/elastic/elasticsearch/issues/28739
    # Need to upgrade to 7 ....

    def docs_query_dual(self, index, field_names, query_string_positive, query_string_negative, filter_kwargs={},
                        sorting={}, pp_from=0, pp_count=None):

        #  default is an empty string
        sub_queries = ''

        #  filters: construct additional query strings
        if filter_kwargs:
            for key, val in filter_kwargs.items():
                sub_queries += ' AND ({}:{})'.format(key, val)

        query_body = {
            "query": {
                "boosting": {
                    "positive": {
                        "query_string": {
                            "fields": field_names,
                            "query": query_string_positive + sub_queries
                        },
                    },
                    "negative": {
                        "query_string": {
                            "fields": field_names,
                            "query": query_string_negative  # TODO: include sub queries?
                        }
                    },
                    "negative_boost": 0.0
                },
            }
        }

        #  sorting
        if sorting:
            query_body["sort"] = self._sorting(sorting)

        if not pp_count:
            pp_count = self.perpage
        res = self.es.search(index=index, body=query_body, from_=pp_from, size=pp_count)
        return res, query_body
